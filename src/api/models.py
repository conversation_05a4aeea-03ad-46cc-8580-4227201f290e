from pydantic import BaseModel
from typing import Optional, Dict, Any, List

# Session Management Models
class InitSessionRequest(BaseModel):
    profile_name: str = "MQL5-THELEGEND"

class InitSessionResponse(BaseModel):
    success: bool
    session_id: Optional[str] = None
    chromedriver_url: Optional[str] = None
    live_preview_url: Optional[str] = None
    profile_name: Optional[str] = None
    status: Optional[str] = None
    error: Optional[str] = None

class GetSessionResponse(BaseModel):
    has_active_session: bool
    session_data: Optional[dict] = None

# Authentication Models
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    success: bool
    message: str
    account_info: Optional[Dict[str, Any]] = None

class AuthCheckResponse(BaseModel):
    success: bool
    is_authenticated: bool
    message: str
    user_info: Optional[Dict[str, Any]] = None

# Messaging Models
class SendMessageRequest(BaseModel):
    user_id: str
    message: Optional[str] = None

class SendMessageResponse(BaseModel):
    success: bool
    message: str

# Chat Models
class ChatConversationMessage(BaseModel):
    message_id: Optional[str] = None  # Generated or extracted if available
    sender_username: Optional[str] = None  # None for own messages
    sender_display_name: Optional[str] = None  # Full display name with titles
    sender_profile_url: Optional[str] = None
    content: str
    timestamp: str
    full_timestamp: str
    is_own_message: bool  # True if chat-message_own class
    message_type: str = "text"  # Could be "text", "system", etc.
    has_reply_option: bool = True
    has_delete_option: bool = False

class ChatDateSection(BaseModel):
    date_title: str  # e.g., "today", "yesterday", "2025.06.16"
    messages: List[ChatConversationMessage]

class ChatConversationResponse(BaseModel):
    success: bool
    message: str
    date_sections: List[ChatDateSection]
    total_messages: int
    participant_info: Optional[Dict[str, Any]] = None

class ChatConversationFlatResponse(BaseModel):
    success: bool
    message: str
    messages: List[ChatConversationMessage]
    total_messages: int
    participant_info: Optional[Dict[str, Any]] = None

class ChatListItem(BaseModel):
    index: int
    chat_id: str
    participant: str
    last_message_preview: str
    timestamp: str
    full_timestamp: str
    is_unread: bool
    href: str

class ChatListResponse(BaseModel):
    success: bool
    message: str
    conversations: List[ChatListItem]
    total_count: int

# Health Check Model
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    has_active_session: bool
    session_id: Optional[str] = None
    live_preview_url: Optional[str] = None
    active_jobs: int
    total_jobs: int
