from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import os
from dotenv import load_dotenv
from .models import (
    InitSessionRequest, InitSessionResponse, GetSessionResponse,
    ChatListResponse, ChatConversationResponse, ChatConversationFlatResponse, SendMessageRequest, SendMessageResponse,
    LoginRequest, LoginResponse, AuthCheckResponse, HealthResponse
)
from ..automation.flow.session_manager import (
    get_current_session, get_or_create_session, terminate_current_session,
    check_authentication, login_to_platform
)
from ..automation.messaging.message_extractor import MessageExtractor
from ..automation.flow.automation_utils import get_driver_from_session
from ..automation.messaging.message_sender import send_message_to_user

load_dotenv()

app = FastAPI(title="Voyagr Browser Automation API")

@app.get("/health", response_model=HealthResponse)
def health_check():
    """Health check endpoint"""
    session_data = get_current_session()

    return HealthResponse(
        status="healthy",
        timestamp=str(__import__("datetime").datetime.now()),
        has_active_session=session_data is not None,
        session_id=session_data.get("session_id") if session_data else None,
        live_preview_url=session_data.get("live_preview_url") if session_data else None,
        active_jobs=0,
        total_jobs=2
    )

# Session Management Endpoints
@app.post("/session/init", response_model=InitSessionResponse)
def init_session(request: InitSessionRequest):
    """
    Initialize or get a persistent browser session with the specified profile
    Returns session information for subsequent automations
    """
    result = get_or_create_session(request.profile_name)

    if result["success"]:
        return InitSessionResponse(
            success=True,
            session_id=result["session_id"],
            chromedriver_url=result["chromedriver_url"],
            live_preview_url=result.get("live_preview_url"),
            profile_name=request.profile_name,
            status=result["status"]
        )
    else:
        return InitSessionResponse(
            success=False,
            error=result.get("error", "Unknown error")
        )

@app.get("/session/current", response_model=GetSessionResponse)
def get_current_session_info():
    """
    Get information about the current active session from Airtop API
    """
    session_data = get_current_session()
    return GetSessionResponse(
        has_active_session=session_data is not None,
        session_data=session_data
    )

@app.delete("/session/current")
def terminate_current_session_endpoint():
    """
    Terminate the current active session
    """
    result = terminate_current_session()
    return JSONResponse(content=result)

# Authentication Endpoints
@app.get("/auth/check", response_model=AuthCheckResponse)
def check_auth():
    """
    Check if user is authenticated on MQL5 platform
    """
    result = check_authentication()
    return AuthCheckResponse(
        success=result["success"],
        is_authenticated=result.get("is_authenticated", False),
        message=result["message"],
        user_info=result.get("user_info")
    )

@app.post("/auth/login", response_model=LoginResponse)
def login(request: LoginRequest):
    """
    Login to MQL5 platform with provided credentials
    """
    result = login_to_platform(request.username, request.password)
    return LoginResponse(
        success=result["success"],
        message=result["message"],
        account_info=result.get("account_info")
    )

# Chat and Messaging Endpoints
@app.get("/chats", response_model=ChatListResponse)
def get_chat_list():
    """
    Extract all chat conversations from the MQL5 messages page
    """
    try:
        # Ensure we have an active session
        session_result = get_or_create_session()
        if not session_result["success"]:
            raise HTTPException(status_code=500, detail=f"Session error: {session_result.get('error', 'Unknown error')}")

        # Test connection to session
        driver = get_driver_from_session()
        if not driver:
            raise HTTPException(status_code=500, detail="Failed to connect to browser session")

        # Initialize message extractor and get chat list using existing driver
        extractor = MessageExtractor()
        result = extractor.get_chat_list(driver)

        return ChatListResponse(**result)

    except Exception as e:
        return ChatListResponse(
            success=False,
            message=f"Error extracting chats: {str(e)}",
            conversations=[],
            total_count=0
        )

@app.post("/chats/{chat_id}/messages", response_model=ChatConversationFlatResponse)
def get_chat_conversation(chat_id: str):
    """
    Navigate to a specific chat and extract the conversation messages
    """
    try:
        # Ensure we have an active session
        session_result = get_or_create_session()
        if not session_result["success"]:
            raise HTTPException(status_code=500, detail=f"Session error: {session_result.get('error', 'Unknown error')}")

        # Test connection to session
        driver = get_driver_from_session()
        if not driver:
            raise HTTPException(status_code=500, detail="Failed to connect to browser session")

        # Initialize message extractor
        extractor = MessageExtractor()

        # Navigate to the specific conversation using existing driver
        nav_result = extractor.navigate_to_conversation(chat_id, driver)
        if not nav_result["success"]:
            raise HTTPException(status_code=500, detail=f"Navigation failed: {nav_result['message']}")

        # Extract conversation messages using existing driver (flat format)
        conv_result = extractor.extract_conversation_flat(driver)

        return ChatConversationFlatResponse(**conv_result)

    except Exception as e:
        return ChatConversationFlatResponse(
            success=False,
            message=f"Error extracting conversation: {str(e)}",
            messages=[],
            total_messages=0
        )

@app.post("/messages/send", response_model=SendMessageResponse)
def send_message(request: SendMessageRequest):
    """
    Send a message to a user by navigating to their chat and sending the message content.
    """
    result = send_message_to_user(request.user_id, request.message)
    return SendMessageResponse(success=result["success"], message=result["message"])
