from ..flow.automation_utils import get_driver_from_session, safe_send_keys, safe_click, wait_for_element, navigate_to_url
from .message_extractor import MessageExtractor
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException


def send_message_to_user(user_id: str, message: str) -> dict:
    """
    Navigates to the user's profile page, clicks 'Send message', and waits for the message box.
    Returns a dict with success status and message.
    """
    driver = get_driver_from_session()
    if not driver:
        return {"success": False, "message": "No active session available"}

    # 1. Navigate to the user's profile page
    profile_url = f"https://www.mql5.com/en/users/{user_id}"
    try:
        driver.get(profile_url)
    except Exception as e:
        return {"success": False, "message": f"Failed to navigate to profile: {str(e)}"}

    # 2. Locate and click the 'Send message' button
    send_btn_locator = (By.CSS_SELECTOR, "button.button_white-and-blue.qa-send-message")
    if not safe_click(driver, send_btn_locator, timeout=10):
        return {"success": False, "message": "Send message button not found or not clickable"}

    # 3. Wait for the message box (textarea) to appear
    textarea_locator = (By.CSS_SELECTOR, "textarea")
    textarea = wait_for_element(driver, textarea_locator, timeout=10)
    if not textarea:
        return {"success": False, "message": "Message textarea not found after clicking send message"}

    # 4. Re-locate the textarea before sending keys to avoid stale element reference
    textarea = wait_for_element(driver, textarea_locator, timeout=10)
    if not textarea:
        return {"success": False, "message": "Message textarea not found (stale after DOM update)"}
    if not safe_send_keys(driver, textarea_locator, message, timeout=10):
        return {"success": False, "message": "Failed to enter message in textarea"}

    # 5. Locate and click the send button (always re-locate, do not reuse old references)
    send_btn_locator = (By.CSS_SELECTOR, "button.button_green.button_small.button_widget[title='Send']")
    if not safe_click(driver, send_btn_locator, timeout=10):
        return {"success": False, "message": "Send button not found or not clickable after entering message"}

    return {"success": True, "message": "Message sent successfully"}
