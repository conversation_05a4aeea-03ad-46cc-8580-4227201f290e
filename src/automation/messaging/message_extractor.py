from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.support.ui import WebDriverWait
import logging
import re
import time
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..flow.automation_utils import get_driver_from_session

logger = logging.getLogger(__name__)

class MessageExtractor:
    """Handles message extraction operations for MQL5 platform"""

    def __init__(self, base_url: str = "https://www.mql5.com"):
        self.base_url = base_url

    def _convert_timestamp_to_date(self, timestamp: str, date_context: str = "") -> str:
        """Convert relative timestamps like 'yesterday', 'today' to actual dates"""
        try:
            if not timestamp or not timestamp.strip():
                return timestamp

            now = datetime.now()
            timestamp = timestamp.strip()

            logger.debug(f"Converting timestamp: '{timestamp}' with context: '{date_context}'")

            # Handle relative date contexts from date bars
            base_date = now
            if date_context.lower() == "yesterday":
                base_date = now - timedelta(days=1)
            elif date_context.lower() == "today":
                base_date = now
            elif re.match(r'\d{4}\.\d{2}\.\d{2}', date_context):
                # Parse explicit date like "2025.06.16"
                try:
                    base_date = datetime.strptime(date_context, "%Y.%m.%d")
                except:
                    pass

            # Handle full timestamps like "2025.06.18 12:12" or "2025-07-18 12:12"
            if re.match(r'\d{4}[.-]\d{2}[.-]\d{2} \d{1,2}:\d{2}', timestamp):
                try:
                    # Try different date formats
                    for fmt in ["%Y.%m.%d %H:%M", "%Y-%m-%d %H:%M", "%Y/%m/%d %H:%M"]:
                        try:
                            result_datetime = datetime.strptime(timestamp, fmt)
                            converted = result_datetime.strftime("%Y-%m-%d %H:%M:%S")
                            logger.debug(f"Converted full timestamp '{timestamp}' to '{converted}'")
                            return converted
                        except ValueError:
                            continue
                except Exception as e:
                    logger.warning(f"Failed to parse full timestamp '{timestamp}': {e}")

            # Handle time formats like "12:12", "19:01", "yesterday", "today"
            if re.match(r'\d{1,2}:\d{2}', timestamp):
                try:
                    time_parts = timestamp.split(':')
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])

                    # Combine base date with time
                    result_datetime = base_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
                    converted = result_datetime.strftime("%Y-%m-%d %H:%M:%S")
                    logger.debug(f"Converted time '{timestamp}' to '{converted}' using base date {base_date.date()}")
                    return converted
                except Exception as e:
                    logger.warning(f"Failed to parse time '{timestamp}': {e}")

            # Handle relative dates like "yesterday", "today"
            if timestamp.lower() in ["yesterday", "вчера"]:
                yesterday = now - timedelta(days=1)
                converted = yesterday.strftime("%Y-%m-%d 23:59:00")  # Assume end of day for relative dates
                logger.debug(f"Converted relative date '{timestamp}' to '{converted}'")
                return converted
            elif timestamp.lower() in ["today", "сегодня"]:
                converted = now.strftime("%Y-%m-%d %H:%M:%S")
                logger.debug(f"Converted relative date '{timestamp}' to '{converted}'")
                return converted

            # Handle date-only formats like "18 Jul", "Jul 18", "18.07", etc.
            date_patterns = [
                (r'\d{1,2} [A-Za-z]{3}', "%d %b"),  # "18 Jul"
                (r'[A-Za-z]{3} \d{1,2}', "%b %d"),  # "Jul 18"
                (r'\d{1,2}\.\d{1,2}', "%d.%m"),     # "18.07"
                (r'\d{1,2}/\d{1,2}', "%d/%m"),      # "18/7"
            ]

            for pattern, fmt in date_patterns:
                if re.match(pattern, timestamp):
                    try:
                        # For date-only formats, use current year and assume end of day
                        current_year = now.year
                        full_date_str = f"{timestamp} {current_year}"
                        if fmt == "%d %b":
                            full_fmt = f"{fmt} %Y"
                        elif fmt == "%b %d":
                            full_fmt = f"{fmt} %Y"
                        else:
                            full_fmt = f"{fmt} %Y"
                            full_date_str = f"{timestamp}.{current_year}"

                        result_datetime = datetime.strptime(full_date_str, full_fmt)
                        # Set to end of day for date-only timestamps
                        result_datetime = result_datetime.replace(hour=23, minute=59, second=0)
                        converted = result_datetime.strftime("%Y-%m-%d %H:%M:%S")
                        logger.debug(f"Converted date '{timestamp}' to '{converted}'")
                        return converted
                    except Exception as e:
                        logger.debug(f"Failed to parse date pattern '{timestamp}': {e}")
                        continue

            # Return original if can't parse
            logger.debug(f"No conversion applied to timestamp: '{timestamp}'")
            return timestamp

        except Exception as e:
            logger.warning(f"Failed to convert timestamp '{timestamp}': {e}")
            return timestamp

    def _human_like_wait(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """Add human-like delay"""
        import random
        wait_time = random.uniform(min_seconds, max_seconds)
        time.sleep(wait_time)

    def get_chat_list(self, driver=None) -> Dict[str, Any]:
        """
        Extract all chat conversations from the messages page
        Returns: ChatListResponse format
        """
        if driver is None:
            driver = get_driver_from_session()

        if not driver:
            logger.error("❌ No driver available")
            return {
                "success": False,
                "message": "No active session available",
                "conversations": [],
                "total_count": 0
            }

        try:
            # Navigate to messages page
            messages_url = f"{self.base_url}/en/messages"
            logger.info(f"🔗 Navigating to messages page: {messages_url}")
            driver.get(messages_url)

            # Wait for page to load
            self._human_like_wait(2, 4)

            # Try to find chat container
            logger.info("🔍 Looking for chat container...")

            container_selectors = [
                ".chat-panel.chat-panel_standalone.chat-panel_width-choice-type",
                ".chat-panel__list",
                ".chat-panel",
                "#chatContainer"
            ]

            container = None
            wait = WebDriverWait(driver, 10)

            for selector in container_selectors:
                try:
                    container = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    logger.info(f"✅ Found chat container with selector: {selector}")
                    break
                except TimeoutException:
                    logger.warning(f"⚠️ Container not found with selector: {selector}")
                    continue

            if not container:
                logger.error("❌ No chat container found")
                return {
                    "success": False,
                    "message": "Chat container not found",
                    "conversations": [],
                    "total_count": 0
                }

            # Extract chat lines from container
            conversations = self._extract_chat_lines(container)

            return {
                "success": True,
                "message": f"Successfully extracted {len(conversations)} conversations",
                "conversations": conversations,
                "total_count": len(conversations)
            }

        except Exception as e:
            logger.error(f"❌ Error getting chat list: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "conversations": [],
                "total_count": 0
            }

    def _extract_chat_lines(self, container) -> List[Dict[str, Any]]:
        """Extract chat lines from the chat container - returns ChatListItem format"""
        try:
            # Look for chat line elements within container
            chat_selectors = [
                "a.chat-line",  # Direct <a> elements with chat-line class
                ".chat-line",
                "a[href*='/messages/']",
                "[class*='chat-line']"
            ]

            chat_elements = []
            for selector in chat_selectors:
                elements = container.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"✅ Found {len(elements)} chat elements with selector: {selector}")
                    chat_elements = elements
                    break

            if not chat_elements:
                logger.warning("⚠️ No chat elements found in container")
                return []

            conversations = []
            for index, chat_element in enumerate(chat_elements):
                try:
                    conv_data = self._extract_conversation_summary(chat_element, index)
                    if conv_data:
                        conversations.append(conv_data)
                except Exception as e:
                    logger.warning(f"Failed to extract conversation {index}: {e}")
                    continue

            logger.info(f"✅ Successfully extracted {len(conversations)} conversations")
            return conversations

        except Exception as e:
            logger.error(f"❌ Error extracting chat lines: {e}")
            return []

    def _extract_conversation_summary(self, chat_element, index: int) -> Optional[Dict[str, Any]]:
        """Extract conversation summary data from a chat element - returns ChatListItem format"""
        try:
            # Extract href/chat_id
            chat_id = ""
            href = chat_element.get_attribute("href") or ""
            if href:
                # Extract chat ID from URL like /en/messages/0131b6ed55d1db01
                match = re.search(r'/messages/([a-f0-9]+)', href)
                if match:
                    chat_id = match.group(1)

            # Extract participant name
            participant = ""
            participant_selectors = [
                ".chat-line__name-text",
                ".chat-line__name",
                ".chat-name",
                ".participant-name",
                "[class*='name']",
                ".username",
                ".sender"
            ]

            for selector in participant_selectors:
                try:
                    name_element = chat_element.find_element(By.CSS_SELECTOR, selector)
                    participant = name_element.text.strip()
                    if participant:
                        break
                except NoSuchElementException:
                    continue

            # Extract last message preview
            last_message_preview = ""
            message_selectors = [
                ".chat-line__desc",
                ".chat-line__message",
                ".last-message",
                ".message-preview",
                "[class*='desc']",
                "[class*='message']"
            ]

            for selector in message_selectors:
                try:
                    message_element = chat_element.find_element(By.CSS_SELECTOR, selector)
                    last_message_preview = message_element.text.strip()
                    if last_message_preview:
                        break
                except NoSuchElementException:
                    continue

            # Extract timestamp
            timestamp = ""
            timestamp_selectors = [
                ".chat-line__date",
                ".timestamp",
                ".date",
                "[class*='time']",
                "[class*='date']"
            ]

            for selector in timestamp_selectors:
                try:
                    time_element = chat_element.find_element(By.CSS_SELECTOR, selector)
                    timestamp = time_element.text.strip()
                    if timestamp:
                        break
                except NoSuchElementException:
                    continue

            logger.debug(f"Conversation {index} ({participant}): extracted timestamp='{timestamp}'")

            # Check if unread
            is_unread = False
            # Only mark as unread if a direct child with .chat-line__unread exists
            if chat_element.find_elements(By.CSS_SELECTOR, ".chat-line__unread"):
                is_unread = True

            # Compute full timestamp using _convert_timestamp_to_date
            full_timestamp = self._convert_timestamp_to_date(timestamp)

            logger.debug(f"Conversation {index} ({participant}): original='{timestamp}' -> converted='{full_timestamp}'")

            # Return ChatListItem format (cleaned)
            return {
                "index": index,
                "chat_id": chat_id,
                "participant": participant,
                "last_message_preview": last_message_preview,
                "timestamp": timestamp,
                "full_timestamp": full_timestamp,
                "is_unread": is_unread,
                "href": href
            }

        except Exception as e:
            logger.warning(f"Failed to extract conversation summary {index}: {e}")
            return None

    def navigate_to_conversation(self, chat_id: str, driver=None) -> Dict[str, Any]:
        """Navigate to a specific conversation by chat_id"""
        if driver is None:
            driver = get_driver_from_session()

        if not driver:
            return {"success": False, "message": "No active session available"}

        try:
            conversation_url = f"{self.base_url}/en/messages/{chat_id}"
            logger.info(f"🔗 Navigating to conversation: {conversation_url}")

            driver.get(conversation_url)
            self._human_like_wait(2, 4)

            # Verify we're on the conversation page
            if chat_id in driver.current_url:
                logger.info("✅ Successfully navigated to conversation")
                return {
                    "success": True,
                    "message": "Successfully navigated to conversation",
                    "url": driver.current_url
                }
            else:
                logger.error(f"❌ Failed to navigate to conversation. Current URL: {driver.current_url}")
                return {
                    "success": False,
                    "message": f"Failed to navigate to conversation. Current URL: {driver.current_url}"
                }

        except Exception as e:
            logger.error(f"❌ Error navigating to conversation: {e}")
            return {"success": False, "message": str(e)}

    def extract_conversation(self, driver=None) -> Dict[str, Any]:
        """Extract all messages from the current conversation page - returns ChatConversationResponse format"""
        if driver is None:
            driver = get_driver_from_session()

        if not driver:
            return {
                "success": False,
                "message": "No active session available",
                "date_sections": [],
                "total_messages": 0
            }

        try:
            # Wait for conversation container to load
            self._human_like_wait(2, 3)

            logger.info(f"📄 Current page title: {driver.title}")
            logger.info(f"📄 Current URL: {driver.current_url}")

            # Find the conversation container
            conversation_selectors = [
                ".chat-comments-list",  # Main container for messages
                ".chat-window",         # Fallback to chat window
                "#chatContainer"        # Ultimate fallback
            ]

            conversation_container = None
            for selector in conversation_selectors:
                try:
                    conversation_container = driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"✅ Found conversation container with: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not conversation_container:
                logger.error("❌ No conversation container found")
                return {
                    "success": False,
                    "message": "No conversation container found",
                    "date_sections": [],
                    "total_messages": 0
                }

            # Extract date sections (.chat-date-bar)
            date_sections = conversation_container.find_elements(By.CSS_SELECTOR, ".chat-date-bar")

            if not date_sections:
                # If no date sections, try to extract messages directly
                logger.info("No date sections found, extracting messages directly")
                messages = self._extract_messages_direct(conversation_container)
                if messages:
                    result_sections = [{
                        "date_title": "today",
                        "messages": messages
                    }]
                    return {
                        "success": True,
                        "message": f"Successfully extracted {len(messages)} messages",
                        "date_sections": result_sections,
                        "total_messages": len(messages)
                    }
                return {
                    "success": False,
                    "message": "No messages found",
                    "date_sections": [],
                    "total_messages": 0
                }

            result_sections = []
            total_messages = 0

            for section in date_sections:
                date_title = self._extract_date_title(section)
                messages = self._extract_messages_from_section(section, date_title, driver)

                if messages:
                    result_sections.append({
                        "date_title": date_title,
                        "messages": messages
                    })
                    total_messages += len(messages)

            logger.info(f"✅ Extracted {len(result_sections)} date sections with {total_messages} total messages")

            return {
                "success": True,
                "message": f"Successfully extracted {total_messages} messages in {len(result_sections)} date sections",
                "date_sections": result_sections,
                "total_messages": total_messages
            }

        except Exception as e:
            logger.error(f"❌ Error extracting conversation: {e}")
            return {
                "success": False,
                "message": str(e),
                "date_sections": [],
                "total_messages": 0
            }

    def _extract_date_title(self, section_element) -> str:
        """Extract date title from a date section"""
        try:
            title_selectors = [
                ".chat-date-bar__title",
                ".date-title",
                ".chat-date-title"
            ]

            for selector in title_selectors:
                try:
                    title_element = section_element.find_element(By.CSS_SELECTOR, selector)
                    return title_element.text.strip()
                except NoSuchElementException:
                    continue

            # Fallback: return the text content of the element itself
            title_text = section_element.text.strip()
            return title_text if title_text else "unknown"

        except Exception as e:
            logger.warning(f"Failed to extract date title: {e}")
            return "unknown"

    def _extract_messages_from_section(self, section_element, date_context: str = "", driver=None) -> List[Dict[str, Any]]:
        """Extract messages that come after a date section - returns ChatConversationMessage format"""
        try:
            if driver is None:
                driver = get_driver_from_session()

            if not driver:
                return []

            messages = []
            current_element = section_element
            last_sender_info = None  # Track last known sender info

            # Find all following .chat-message elements until the next .chat-date-bar
            while True:
                try:
                    # Get next sibling element
                    current_element = driver.execute_script(
                        "return arguments[0].nextElementSibling;", current_element
                    )

                    if not current_element:
                        break

                    # If we hit another date bar, stop
                    if "chat-date-bar" in (current_element.get_attribute("class") or ""):
                        break

                    # If it's a chat message, extract it
                    if "chat-message" in (current_element.get_attribute("class") or ""):
                        message_data = self._extract_message_data_with_context(current_element, date_context, last_sender_info)
                        if message_data:
                            messages.append(message_data)

                            # Update last_sender_info only when explicit sender info exists
                            if not message_data.get("is_own_message") and message_data.get("sender_username"):
                                last_sender_info = {
                                    "sender_username": message_data["sender_username"],
                                    "sender_display_name": message_data["sender_display_name"],
                                    "sender_profile_url": message_data["sender_profile_url"]
                                }
                            # Keep existing sender info - do not reset for own messages
                except Exception as e:
                    logger.warning(f"Error processing sibling element: {e}")
                    break

            return messages

        except Exception as e:
            logger.warning(f"Failed to extract messages from section: {e}")
            return []

    def _extract_messages_direct(self, container) -> List[Dict[str, Any]]:
        """Extract messages directly from container when no date sections exist"""
        try:
            # Look for .chat-message elements directly
            message_elements = container.find_elements(By.CSS_SELECTOR, ".chat-message")

            messages = []
            last_sender_info = None  # Track last known sender info

            for msg_element in message_elements:
                message_data = self._extract_message_data_with_context(msg_element, "today", last_sender_info)
                if message_data:
                    messages.append(message_data)

                    # Update last_sender_info only when explicit sender info exists
                    if not message_data.get("is_own_message") and message_data.get("sender_username"):
                        last_sender_info = {
                            "sender_username": message_data["sender_username"],
                            "sender_display_name": message_data["sender_display_name"],
                            "sender_profile_url": message_data["sender_profile_url"]
                        }
                            # Keep existing sender info - do not reset for own messages
            logger.info(f"✅ Extracted {len(messages)} messages directly")
            return messages

        except Exception as e:
            logger.warning(f"Failed to extract messages directly: {e}")
            return []

    def _extract_message_data_with_context(self, message_element, date_context: str = "today", last_sender_info: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
        """Extract data from a single message element with context from previous messages - returns ChatConversationMessage format"""
        try:
            # Check if it's own message
            message_classes = message_element.get_attribute("class") or ""
            is_own_message = "chat-message_own" in message_classes

            # Extract content from .chat-message__text
            content = ""
            try:
                content_element = message_element.find_element(By.CSS_SELECTOR, ".chat-message__text")
                content = content_element.text.strip()
            except NoSuchElementException:
                # Fallback to message element text
                content = message_element.text.strip()

            # Extract timestamp from .chat-message__date
            timestamp = ""
            timestamp_selectors = [
                ".chat-message__date",
                ".timestamp",
                "[title*='2025']"  # Look for title attributes with dates
            ]

            for selector in timestamp_selectors:
                try:
                    time_element = message_element.find_element(By.CSS_SELECTOR, selector)
                    timestamp = time_element.text.strip()
                    if timestamp:
                        break
                except NoSuchElementException:
                    continue

            # Extract sender info
            sender_username = None
            sender_display_name = None
            sender_profile_url = None

            # Try to extract sender info from the message (regardless of is_own_message status)
            logger.debug(f"Attempting to extract sender info (is_own_message={is_own_message})")
            try:
                # Look for the sender name in .chat-message__name a element
                name_element = message_element.find_element(By.CSS_SELECTOR, ".chat-message__name a")
                sender_text = name_element.text.strip()
                sender_profile_url = name_element.get_attribute("href")
                if sender_text:
                    sender_display_name = sender_text
                    # Extract just the username (remove any additional text like "Legend")
                    sender_username = sender_text.split()[0] if " " in sender_text else sender_text
                    logger.debug(f"Found explicit sender info in message: {sender_username}")
            except NoSuchElementException:
                logger.debug(f"No .chat-message__name a found, trying fallback selectors")
                # Fallback to other selectors
                sender_selectors = [".chat-message__name", "[class*='name']"]
                for selector in sender_selectors:
                    try:
                        sender_element = message_element.find_element(By.CSS_SELECTOR, selector)
                        sender_text = sender_element.text.strip()
                        if sender_text:
                            sender_username = sender_text.split()[0] if " " in sender_text else sender_text
                            sender_display_name = sender_text
                            logger.debug(f"Found explicit sender info via fallback: {sender_username}")
                            break
                    except NoSuchElementException:
                        continue

            # If no explicit sender info found and we have last_sender_info, use it for ALL messages
            if not sender_username and last_sender_info:
                sender_username = last_sender_info.get("sender_username")
                sender_display_name = last_sender_info.get("sender_display_name")
                sender_profile_url = last_sender_info.get("sender_profile_url")
                logger.debug(f"Applied last known sender info: {sender_username} (is_own_message={is_own_message})")
            elif not sender_username:
                logger.debug(f"No sender info found and no last_sender_info available")

            # Check for action buttons to determine permissions
            has_delete_option = False
            has_reply_option = True

            try:
                # Look for delete button in dropdown menu
                delete_elements = message_element.find_elements(By.CSS_SELECTOR, ".chat-window-menu__item_red")
                has_delete_option = len(delete_elements) > 0

                # Look for reply button
                reply_elements = message_element.find_elements(By.CSS_SELECTOR, "[class*='reply']")
                has_reply_option = len(reply_elements) > 0 or not is_own_message
            except Exception as e:
                logger.debug(f"Could not determine message options: {e}")

            # Generate a simple message ID from timestamp and content hash
            message_id = None
            if timestamp and content:
                hash_input = f"{timestamp}_{content[:50]}_{sender_username or 'own'}"
                message_id = hashlib.md5(hash_input.encode()).hexdigest()[:16]

            # Convert timestamp to actual date
            full_timestamp = self._convert_timestamp_to_date(timestamp, date_context)

            # Return ChatConversationMessage format
            return {
                "message_id": message_id,
                "sender_username": sender_username,
                "sender_display_name": sender_display_name,
                "sender_profile_url": sender_profile_url,
                "content": content,
                "timestamp": timestamp,  # Keep original format
                "full_timestamp": full_timestamp,  # Converted format
                "is_own_message": is_own_message,
                "message_type": "text",
                "has_reply_option": has_reply_option,
                "has_delete_option": has_delete_option
            }

        except Exception as e:
            logger.warning(f"Failed to extract message data: {e}")
            return None

    def _extract_message_data(self, message_element, date_context: str = "today") -> Optional[Dict[str, Any]]:
        """Legacy method - calls the new context-aware method for backward compatibility"""
        return self._extract_message_data_with_context(message_element, date_context, None)

    def extract_conversation_flat(self, driver=None) -> Dict[str, Any]:
        """Extract all messages from the current conversation page as a flat array - returns flat message list"""
        if driver is None:
            driver = get_driver_from_session()

        if not driver:
            return {
                "success": False,
                "message": "No active session available",
                "messages": [],
                "total_messages": 0
            }

        try:
            # Wait for conversation container to load
            self._human_like_wait(2, 3)

            logger.info(f"📄 Current page title: {driver.title}")
            logger.info(f"📄 Current URL: {driver.current_url}")

            # Find the conversation container
            conversation_selectors = [
                ".chat-comments-list",  # Main container for messages
                ".chat-window",         # Fallback to chat window
                "#chatContainer"        # Ultimate fallback
            ]

            conversation_container = None
            for selector in conversation_selectors:
                try:
                    conversation_container = driver.find_element(By.CSS_SELECTOR, selector)
                    logger.info(f"✅ Found conversation container with: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not conversation_container:
                logger.error("❌ No conversation container found")
                return {
                    "success": False,
                    "message": "No conversation container found",
                    "messages": [],
                    "total_messages": 0
                }

            # Extract all messages in order (both date bars and messages)
            all_elements = conversation_container.find_elements(By.CSS_SELECTOR, ".chat-date-bar, .chat-message")

            if not all_elements:
                logger.info("No messages or date bars found")
                return {
                    "success": False,
                    "message": "No messages found",
                    "messages": [],
                    "total_messages": 0
                }

            all_messages = []
            last_sender_info = None
            current_date_context = "today"

            for i, element in enumerate(all_elements):
                element_class = element.get_attribute("class") or ""

                # If it's a date bar, update the date context
                if "chat-date-bar" in element_class:
                    current_date_context = self._extract_date_title(element)
                    logger.debug(f"Element {i}: Date bar - Updated date context to: {current_date_context}")

                # If it's a chat message, extract it with current context
                elif "chat-message" in element_class:
                    is_own = "chat-message_own" in element_class
                    logger.debug(f"Element {i}: Chat message - is_own={is_own}, current_last_sender={last_sender_info.get('sender_username') if last_sender_info else None}")

                    message_data = self._extract_message_data_with_context(element, current_date_context, last_sender_info)
                    if message_data:
                        logger.debug(f"Element {i}: Extracted message - sender='{message_data.get('sender_username')}', content='{message_data.get('content', '')[:20]}...', is_own={message_data.get('is_own_message')}")
                        all_messages.append(message_data)

                        # Update last_sender_info if this message has explicit sender info (regardless of is_own_message)
                        # Check if this message has sender info that was explicitly found in HTML (not inherited)
                        message_has_explicit_sender = False
                        if message_data.get("sender_username") and not message_data.get("is_own_message"):
                            # Check if this was found directly or inherited by looking at the DOM
                            try:
                                name_element = element.find_element(By.CSS_SELECTOR, ".chat-message__name a")
                                if name_element.text.strip():
                                    message_has_explicit_sender = True
                            except:
                                try:
                                    name_element = element.find_element(By.CSS_SELECTOR, ".chat-message__name")
                                    if name_element.text.strip():
                                        message_has_explicit_sender = True
                                except:
                                    pass

                        if message_has_explicit_sender:
                            last_sender_info = {
                                "sender_username": message_data["sender_username"],
                                "sender_display_name": message_data["sender_display_name"],
                                "sender_profile_url": message_data["sender_profile_url"]
                            }
                            logger.debug(f"Element {i}: Updated global sender info to: {message_data['sender_username']} (explicit)")
                        else:
                            logger.debug(f"Element {i}: Keeping previous sender info (no explicit sender found)")
                    else:
                        logger.warning(f"Element {i}: Failed to extract message data")

            logger.info(f"✅ Extracted {len(all_messages)} messages in flat format")

            return {
                "success": True,
                "message": f"Successfully extracted {len(all_messages)} messages",
                "messages": all_messages,
                "total_messages": len(all_messages)
            }

        except Exception as e:
            logger.error(f"❌ Error extracting conversation: {e}")
            return {
                "success": False,
                "message": str(e),
                "messages": [],
                "total_messages": 0
            }
