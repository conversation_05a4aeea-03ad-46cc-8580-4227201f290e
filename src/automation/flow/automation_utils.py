from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.remote_connection import ChromeRemoteConnection
from typing import Optional
import os
import requests
from dotenv import load_dotenv
from .session_manager import get_current_session

load_dotenv()

def create_airtop_selenium_connection(airtop_api_key, chromedriver_url, *args, **kwargs):
    class AirtopRemoteConnection(ChromeRemoteConnection):
        @classmethod
        def get_remote_connection_headers(cls, *args, **kwargs):
            headers = super().get_remote_connection_headers(*args, **kwargs)
            headers['Authorization'] = f'Bearer {airtop_api_key}'
            return headers

    return AirtopRemoteConnection(remote_server_addr=chromedriver_url, *args, **kwargs)

def get_driver_from_session() -> Optional[webdriver.Remote]:
    """
    Get a Selenium WebDriver connected to the current active session
    Returns None if no active session exists
    """
    session_data = get_current_session()
    if not session_data or not session_data.get("chromedriver_url"):
        return None

    try:
        # Get API key for authentication
        api_key = os.getenv("AIRTOP_API_KEY")
        if not api_key:
            print("Error: AIRTOP_API_KEY not found in environment")
            return None

        chromedriver_url = session_data["chromedriver_url"]
        print(f"Connecting to ChromeDriver: {chromedriver_url}")

        # Create Chrome options
        options = Options()

        # Create driver using the official Airtop method
        driver = webdriver.Remote(
            command_executor=create_airtop_selenium_connection(api_key, chromedriver_url),
            options=options
        )

        print("✅ Successfully connected to Airtop session!")
        return driver

    except Exception as e:
        print(f"Error connecting to session: {e}")
        return None

def navigate_to_url(url: str, timeout: int = 10) -> dict:
    """
    Navigate to a URL using the current session
    """
    driver = get_driver_from_session()
    if not driver:
        return {"success": False, "error": "No active session available"}

    try:
        driver.get(url)
        # Wait for page to load
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        return {
            "success": True,
            "url": driver.current_url,
            "title": driver.title
        }
    except TimeoutException:
        return {"success": False, "error": f"Page load timeout after {timeout} seconds"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def get_page_info() -> dict:
    """
    Get current page information from the active session
    """
    driver = get_driver_from_session()
    if not driver:
        return {"success": False, "error": "No active session available"}

    try:
        return {
            "success": True,
            "url": driver.current_url,
            "title": driver.title,
            "page_source_length": len(driver.page_source)
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

def wait_for_element(driver: webdriver.Remote, locator: tuple, timeout: int = 10):
    """
    Wait for an element to be present and return it
    """
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located(locator)
        )
        return element
    except TimeoutException:
        print(f"Element {locator} not found within {timeout} seconds")
        return None

def safe_click(driver: webdriver.Remote, locator: tuple, timeout: int = 10) -> bool:
    """
    Safely click an element with wait
    """
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable(locator)
        )
        element.click()
        return True
    except TimeoutException:
        print(f"Element {locator} not clickable within {timeout} seconds")
        return False
    except Exception as e:
        print(f"Error clicking element {locator}: {e}")
        return False

def safe_send_keys(driver: webdriver.Remote, locator: tuple, text: str, timeout: int = 10) -> bool:
    """
    Safely send keys to an element with wait
    """
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located(locator)
        )
        element.clear()
        element.send_keys(text)
        return True
    except TimeoutException:
        print(f"Element {locator} not found within {timeout} seconds")
        return False
    except Exception as e:
        print(f"Error sending keys to element {locator}: {e}")
        return False
