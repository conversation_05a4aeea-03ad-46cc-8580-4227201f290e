from airtop import Airtop, SessionConfigV1
import os
import json
from typing import Optional, Dict, Any, List
from datetime import datetime
from dotenv import load_dotenv
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time

load_dotenv()

AIRTOP_API_KEY = os.getenv("AIRTOP_API_KEY")
SESSION_FILE = "active_session.json"  # Keep for backward compatibility but don't rely on it

class SessionManager:
    """Manages Airtop browser sessions for persistent automation"""

    def __init__(self, profile_name: str = "MQL5-THELEGEND"):
        self.profile_name = profile_name
        self.client = Airtop(api_key=AIRTOP_API_KEY)
        self.session_data = None

    def get_current_session_from_airtop(self) -> Optional[Dict[str, Any]]:
        """
        Get the current active session directly from Airtop API
        This is the primary method - no reliance on local JSON
        """
        try:
            sessions_response = self.client.sessions.list()

            if not sessions_response.data or not sessions_response.data.sessions:
                return None

            # Find the most recent active session with our profile
            for session in sessions_response.data.sessions:
                if session.status in ["active", "running"]:
                    # Check if this session matches our profile
                    session_info = {
                        "session_id": session.id,
                        "chromedriver_url": f"https://api.airtop.ai/chromedriver/{session.id}",
                        "live_preview_url": f"https://portal.airtop.ai/sessions/{session.id}",
                        "profile_name": self.profile_name,
                        "status": session.status,
                        "created_at": session.created_at if hasattr(session, 'created_at') else datetime.now().isoformat()
                    }

                    # Update local file for backward compatibility
                    self._save_session_to_file(session_info)
                    return session_info

            return None

        except Exception as e:
            print(f"Error getting session from Airtop: {e}")
            return None

    def get_or_create_session(self) -> Dict[str, Any]:
        """
        Get existing session from Airtop API or create a new one if none exists
        """
        # First check Airtop API directly
        existing_session = self.get_current_session_from_airtop()
        if existing_session:
            print(f"Using existing session: {existing_session['session_id']}")
            return {
                "success": True,
                **existing_session
            }

        # No active session found, create a new one
        print("Creating new session...")
        return self.initialize_session()

    def initialize_session(self) -> Dict[str, Any]:
        """
        Initialize a new Airtop session with the specified profile
        Returns session info including session_id, chromedriver_url, and live_preview_url
        """
        try:
            # Create session configuration
            config = SessionConfigV1(profile_name=self.profile_name)
            session = self.client.sessions.create(configuration=config)

            # Extract session information
            session_info = {
                "session_id": session.data.id,
                "chromedriver_url": session.data.chromedriver_url,
                "cdp_url": session.data.cdp_url,
                "cdp_ws_url": session.data.cdp_ws_url,
                "profile_name": self.profile_name,
                "status": session.data.status,
                "created_at": datetime.now().isoformat(),
                "live_preview_url": f"https://portal.airtop.ai/sessions/{session.data.id}"
            }

            # Store session data locally for backward compatibility
            self.session_data = session_info
            self._save_session_to_file(session_info)

            return {
                "success": True,
                "session_id": session_info["session_id"],
                "chromedriver_url": session_info["chromedriver_url"],
                "live_preview_url": session_info["live_preview_url"],
                "profile_name": session_info["profile_name"],
                "status": session_info["status"]
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "session_id": None,
                "chromedriver_url": None,
                "live_preview_url": None
            }

    def terminate_session(self, session_id: str) -> Dict[str, Any]:
        """
        Terminate a specific session
        """
        try:
            self.client.sessions.terminate(session_id)

            # Clean up local session file
            if os.path.exists(SESSION_FILE):
                os.remove(SESSION_FILE)

            return {"success": True, "message": "Session terminated successfully"}
        except Exception as e:
            return {"success": False, "message": f"Error terminating session: {str(e)}"}

    def check_authentication(self, driver=None) -> Dict[str, Any]:
        """
        Check if we're logged into MQL5 platform
        """
        from .automation_utils import get_driver_from_session

        if driver is None:
            driver = get_driver_from_session()

        if not driver:
            return {
                "success": False,
                "is_authenticated": False,
                "message": "No browser session available"
            }

        try:
            # Navigate to MQL5 messages page to check auth
            driver.get("https://www.mql5.com/en/messages")
            time.sleep(3)

            # Check for login indicators
            try:
                # Look for elements that indicate we're logged in
                profile_elements = driver.find_elements(By.CSS_SELECTOR, ".header-profile, .user-menu, [class*='profile'], [class*='user']")
                login_elements = driver.find_elements(By.CSS_SELECTOR, "a[href*='login'], .login-button, [class*='login']")

                # If we find profile elements and no login prompts, we're likely authenticated
                if profile_elements and not login_elements:
                    # Try to extract user info
                    user_info = self._extract_user_info(driver)
                    return {
                        "success": True,
                        "is_authenticated": True,
                        "message": "User is authenticated",
                        "user_info": user_info
                    }
                else:
                    return {
                        "success": True,
                        "is_authenticated": False,
                        "message": "User is not authenticated - login required"
                    }

            except Exception as e:
                # Fallback: check URL and page content
                current_url = driver.current_url
                page_title = driver.title.lower()

                if "login" in current_url or "login" in page_title:
                    return {
                        "success": True,
                        "is_authenticated": False,
                        "message": "Redirected to login page - authentication required"
                    }
                else:
                    return {
                        "success": True,
                        "is_authenticated": True,
                        "message": "Appears to be authenticated based on URL/title",
                        "user_info": {"url": current_url, "title": driver.title}
                    }

        except Exception as e:
            return {
                "success": False,
                "is_authenticated": False,
                "message": f"Error checking authentication: {str(e)}"
            }

    def login(self, username: str, password: str, driver=None) -> Dict[str, Any]:
        """
        Login to MQL5 platform with provided credentials
        """
        from .automation_utils import get_driver_from_session

        if driver is None:
            driver = get_driver_from_session()

        if not driver:
            return {
                "success": False,
                "message": "No browser session available"
            }

        try:
            # Navigate to login page
            driver.get("https://www.mql5.com/en/auth_login")
            time.sleep(3)  # Wait for page to load

            # Find and fill username/login field - using the actual field name "Login"
            login_selectors = [
                "input[name='Login']",
                "input#Login",
                ".qa-login",
                "input.qa-login"
            ]

            username_field = None
            for selector in login_selectors:
                try:
                    username_field = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if not username_field:
                return {
                    "success": False,
                    "message": "Could not find login field"
                }

            # Fill username
            username_field.clear()
            username_field.send_keys(username)

            # Find and fill password field - using the actual field name "Password"
            password_selectors = [
                "input[name='Password']",
                "input#Password",
                ".qa-password",
                "input.qa-password"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue

            if not password_field:
                return {
                    "success": False,
                    "message": "Could not find password field"
                }

            # Fill password
            password_field.clear()
            password_field.send_keys(password)

            # Find and click login button - using the actual submit button
            login_button_selectors = [
                "input.qa-submit",
                "input#loginSubmit",
                "input[value='Log in']",
                "input[type='submit']",
                ".button_yellow"
            ]

            login_button = None
            for selector in login_button_selectors:
                try:
                    login_button = driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except:
                    continue

            if not login_button:
                return {
                    "success": False,
                    "message": "Could not find login button"
                }

            # Click login button
            login_button.click()
            time.sleep(5)  # Wait for login to process

            # Check if login was successful by looking for redirect or profile elements
            auth_check = self.check_authentication(driver)
            if auth_check.get("is_authenticated"):
                return {
                    "success": True,
                    "message": "Login successful",
                    "account_info": auth_check.get("user_info", {})
                }
            else:
                # Check for error messages on the login page
                error_elements = driver.find_elements(By.CSS_SELECTOR, ".error, .alert, .message, [class*='error']")
                error_message = "Login failed - please check credentials"
                if error_elements:
                    try:
                        error_message = f"Login failed: {error_elements[0].text.strip()}"
                    except:
                        pass

                return {
                    "success": False,
                    "message": error_message
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"Error during login: {str(e)}"
            }

    def _extract_user_info(self, driver) -> Dict[str, Any]:
        """
        Extract user information from the current page
        """
        try:
            user_info = {
                "url": driver.current_url,
                "title": driver.title
            }

            # Try to find username or profile info
            profile_selectors = [
                ".header-profile .username",
                ".user-menu .username",
                "[class*='profile'] [class*='name']",
                "[class*='user'] [class*='name']"
            ]

            for selector in profile_selectors:
                try:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    user_info["username"] = element.text.strip()
                    break
                except:
                    continue

            return user_info

        except Exception as e:
            return {"error": str(e)}

    def get_active_session(self) -> Optional[Dict[str, Any]]:
        """
        Get the currently active session - prefer Airtop API over local file
        """
        # First try to get from Airtop API
        airtop_session = self.get_current_session_from_airtop()
        if airtop_session:
            return airtop_session

        # Fallback to local file for backward compatibility
        try:
            if os.path.exists(SESSION_FILE):
                with open(SESSION_FILE, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"Error reading session file: {e}")
            return None

    def _save_session_to_file(self, session_info: Dict[str, Any]):
        """Save session information to a local file for backward compatibility"""
        try:
            with open(SESSION_FILE, 'w') as f:
                json.dump(session_info, f, indent=2)
        except Exception as e:
            print(f"Error saving session to file: {e}")


# Simplified public functions that use the improved SessionManager
def get_current_session() -> Optional[Dict[str, Any]]:
    """
    Get the current active session information from Airtop API
    """
    manager = SessionManager()
    return manager.get_current_session_from_airtop()

def get_or_create_session(profile_name: str = "MQL5-THELEGEND") -> Dict[str, Any]:
    """
    Get existing session or create a new one
    """
    manager = SessionManager(profile_name)
    return manager.get_or_create_session()

def terminate_current_session() -> Dict[str, Any]:
    """
    Terminate the current active session
    """
    manager = SessionManager()
    current_session = manager.get_current_session_from_airtop()
    if current_session:
        return manager.terminate_session(current_session["session_id"])
    return {"success": True, "message": "No active session to terminate"}

def check_authentication() -> Dict[str, Any]:
    """
    Check if user is authenticated on MQL5 platform
    """
    manager = SessionManager()
    return manager.check_authentication()

def login_to_platform(username: str, password: str) -> Dict[str, Any]:
    """
    Login to MQL5 platform
    """
    manager = SessionManager()
    return manager.login(username, password)
