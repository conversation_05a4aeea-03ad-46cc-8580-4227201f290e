# Voyagr Browser Automation - Deployment Guide

## Overview
This guide covers deploying the Voyagr Browser Automation API to Sevalla using Docker.

## Prerequisites

### Required Environment Variables
- `AIRTOP_API_KEY`: Your Airtop API key for browser automation

### System Requirements
- Docker support
- Internet access for browser automation
- Minimum 1GB RAM, 2GB recommended
- 1 CPU core minimum

## Local Testing

### 1. Build and Test Locally
```bash
# Build the Docker image
docker build -t voyagr-automation .

# Run with environment variables
docker run -p 8000:8000 -e AIRTOP_API_KEY=your_api_key_here voyagr-automation
```

### 2. Using Docker Compose
```bash
# Create .env file with your API key
echo "AIRTOP_API_KEY=your_api_key_here" > .env

# Start the service
docker-compose up -d

# Check logs
docker-compose logs -f

# Stop the service
docker-compose down
```

## Sevalla Deployment

### 1. Prepare Repository
Ensure your code is pushed to the repository:
```bash
git add .
git commit -m "deployment: optimize for production"
git push origin deployment
```

### 2. Sevalla Configuration

#### Environment Variables
Set these in your Sevalla dashboard:
- `AIRTOP_API_KEY`: Your Airtop API key

#### Dockerfile Settings
- **Port**: 8000
- **Health Check**: `/health` endpoint
- **Build Context**: Root directory
- **Dockerfile**: `Dockerfile`

#### Resource Allocation
- **Memory**: 1GB minimum, 2GB recommended
- **CPU**: 1 core minimum
- **Storage**: 5GB for Chrome and dependencies

### 3. Health Monitoring
The application includes a health check endpoint at `/health` that returns:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "has_active_session": true,
  "session_id": "session-id-here",
  "live_preview_url": "https://portal.airtop.ai/sessions/...",
  "active_jobs": 0,
  "total_jobs": 2
}
```

## API Endpoints

### Core Endpoints
- `GET /health` - Health check
- `POST /session/init` - Initialize browser session
- `GET /session/current` - Get current session info
- `DELETE /session/current` - Terminate current session

### Messaging Endpoints
- `GET /chats` - List all conversations
- `GET /chats/{chat_id}/messages` - Get conversation messages
- `POST /messages/send` - Send a message to user

## Troubleshooting

### Common Issues

#### 1. Browser Session Failures
- Verify `AIRTOP_API_KEY` is set correctly
- Check Airtop service status
- Review application logs for connection errors

#### 2. Memory Issues
- Increase container memory allocation
- Monitor browser session cleanup
- Check for memory leaks in logs

#### 3. Port Conflicts
- Ensure port 8000 is available
- Check Sevalla port configuration
- Verify no other services using the same port

### Logs and Monitoring
- Health endpoint provides real-time status
- Application logs available through Sevalla dashboard
- Monitor browser session creation/termination

## Production Considerations

### Security
- Environment variables are properly isolated
- Non-root user in container
- Minimal attack surface with slim base image

### Performance
- Multi-stage Docker build for smaller images
- Optimized dependency installation
- Proper Chrome configuration for headless operation

### Scalability
- Stateless application design
- Session data stored externally (Airtop)
- Horizontal scaling possible with load balancer

## Support
For deployment issues:
1. Check application logs in Sevalla dashboard
2. Verify environment variables are set
3. Test health endpoint accessibility
4. Review resource allocation

## Version Information
- **Python**: 3.12
- **FastAPI**: Latest
- **Chrome**: Latest stable
- **Docker**: Multi-stage build optimized
