[project]
name = "voyagr-browser-automation"
version = "0.1.0"
description = "Browser automation service for MQL5 messaging and interaction"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.34.3",
    "selenium>=4.33.0",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
    "pydantic>=2.0.0",
    "airtop>=0.0.49",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 100
target-version = ['py312']

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
