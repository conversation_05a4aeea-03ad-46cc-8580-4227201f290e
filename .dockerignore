# Python cache and compiled files
__pycache__/
*.pyc
*.pyo
*.pyd
*.so
*.egg-info/

# Virtual environments
.venv/
venv/
env/
ENV/

# Environment and config files
.env
.env.*
active_session.json
session_*.json

# Development files
memory-bank/
test_*.py
*_test.py
tests/

# Git and version control
.git/
.gitignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Documentation and development
README.md
*.md
docs/

# Logs and temporary files
*.log
logs/
tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# Build artifacts
build/
dist/
*.egg-info/

# Coverage and testing
.coverage
.pytest_cache/
htmlcov/

# Node modules (if any)
node_modules/
