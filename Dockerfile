# Clean Dockerfile for Sevalla deployment - follows working pattern
FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# Install system dependencies for Chrome/Selenium
RUN apt-get update && apt-get install -y \
    wget \
    gnupg2 \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    xdg-utils \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome - handle different architectures
RUN ARCH=$(dpkg --print-architecture) && \
    if [ "$ARCH" = "amd64" ]; then \
        wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list && \
        apt-get update && \
        apt-get install -y --no-install-recommends google-chrome-stable; \
    else \
        apt-get update && \
        apt-get install -y --no-install-recommends chromium; \
    fi && \
    rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Cache bust for rebuild - Updated for Voyagr Browser Automation
RUN echo "Voyagr Browser Automation build: $(date) - Airtop integration" > /app/build_info.txt

# Copy requirements.txt first for better layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy project code
COPY . .

# Create startup script for API launch
RUN echo '#!/bin/bash\n\
echo "Starting Voyagr Browser Automation API..."\n\
echo "Environment variables:"\n\
printenv | grep -E "(AIRTOP)" || echo "No AIRTOP environment variables found"\n\
echo "Python path: $PYTHONPATH"\n\
echo "Starting API server..."\n\
exec uvicorn main:app --host 0.0.0.0 --port ${PORT:-8000}\n\
' > /app/start.sh && chmod +x /app/start.sh

# Set Chrome/Chromium environment variables
ENV CHROME_BIN="/usr/bin/google-chrome-stable"
ENV CHROMIUM_BIN="/usr/bin/chromium"

# Expose port (Sevalla will set PORT env var)
EXPOSE ${PORT:-8000}

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8000}/health || exit 1

# Use the startup script
CMD ["/app/start.sh"]
